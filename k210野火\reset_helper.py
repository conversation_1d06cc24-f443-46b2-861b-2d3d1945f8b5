# 重置辅助工具 - 用于快速重置测试
import os

def reset_system():
    """重置系统到初始状态"""
    try:
        # 删除保存的位置文件
        os.remove('/flash/saved_position.txt')
        print("位置文件已删除")
        print("系统已重置，可以重新测试第二问")
        return True
    except:
        print("没有找到位置文件或删除失败")
        return False

def check_saved_position():
    """检查保存的位置"""
    try:
        with open('/flash/saved_position.txt', 'r') as f:
            data = f.read().strip()
            print("当前保存位置:", data)
            return data
    except:
        print("没有保存位置")
        return None

def manual_save_position(x, y):
    """手动保存位置（用于测试）"""
    try:
        with open('/flash/saved_position.txt', 'w') as f:
            f.write("%d,%d" % (x, y))
        print("手动保存位置: %d,%d" % (x, y))
        return True
    except:
        print("保存失败")
        return False

# 使用方法：
# 1. 重置系统：reset_system()
# 2. 检查位置：check_saved_position()
# 3. 手动保存：manual_save_position(2048, 2048)

if __name__ == "__main__":
    print("=== 重置辅助工具 ===")
    print("1. 重置系统")
    print("2. 检查位置")
    print("3. 手动保存测试位置")
    
    choice = input("选择操作 (1/2/3): ")
    
    if choice == "1":
        reset_system()
    elif choice == "2":
        check_saved_position()
    elif choice == "3":
        x = int(input("输入X位置: "))
        y = int(input("输入Y位置: "))
        manual_save_position(x, y)
    else:
        print("无效选择")
