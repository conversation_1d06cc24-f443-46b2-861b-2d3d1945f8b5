# 精简版K210矩形自动追踪系统
# 专注于矩形检测和中心追踪功能

from machine import UART
import time
import sensor, image, lcd
from fpioa_manager import fm
import gc
from math import sqrt

# 硬件初始化
def hardware_init():
    """K210硬件初始化"""
    # 摄像头初始化
    sensor.reset()
    sensor.set_pixformat(sensor.GRAYSCALE)
    sensor.set_framesize(sensor.QQVGA)
    sensor.set_brightness(-2)
    sensor.set_saturation(-2)
    sensor.skip_frames(time=2000)
    
    # LCD初始化
    lcd.init()
    lcd.clear(lcd.WHITE)
    
    print("K210硬件初始化完成")

# 舵机控制函数
def func_servo(id0, posit0, interval0):
    """舵机控制函数"""
    ZT1 = 0xFF
    ZT2 = 0xFF
    DATA1 = 0X2A
    DATA2 = (posit0 >> 8) & 0xff
    DATA3 = posit0 & 0xff
    DATA4 = (interval0 >> 8) & 0xff
    DATA5 = interval0 & 0xff
    data_length = 0x07
    WriteDATA = 0X03
    GetChecksum = (~(id0 + data_length + WriteDATA + DATA1 + DATA2 + DATA3 + DATA4 + DATA5)) & 0xff
    text = bytes([ZT1, ZT2, id0, data_length, WriteDATA, DATA1, DATA2, DATA3, DATA4, DATA5, GetChecksum])
    uart.write(text)

def main():
    """主程序 - 纯矩形追踪功能"""
    global uart
    
    # 硬件初始化
    hardware_init()
    
    # 追踪参数初始化 - 完全按照原代码
    clock = time.clock()
    target_x = 70  # 对应原代码 xin = 70
    target_y = 70  # 对应原代码 yin = 70
    
    # PID控制参数 - 完全保持原代码逻辑
    # 原代码：P = (ZX - xin) * 0, I = (ZX - xin) * 1 + I
    kp_x = 0       # X轴比例系数（原代码为0）
    ki_x = 1       # X轴积分系数（原代码为1）
    kp_y = 0       # Y轴比例系数（原代码为0）
    ki_y = 1       # Y轴积分系数（原代码为1）

    # PID积分项（对应原代码的I和I2）
    integral_x = 0  # 对应原代码的I
    integral_y = 0  # 对应原代码的I2
    
    # 舵机参数
    servo_x_center = 2048  # X轴舵机中心位置
    servo_y_center = 2048  # Y轴舵机中心位置
    servo_x_pos = servo_x_center
    servo_y_pos = servo_y_center
    interval = 100  # 舵机运动时间
    ID1 = 0x01     # X轴舵机ID
    ID2 = 0x02     # Y轴舵机ID

    # 第二问+第三问联合功能
    target_locked_x = None  # 记录锁定时的X轴舵机位置
    target_locked_y = None  # 记录锁定时的Y轴舵机位置
    no_target_count = 0     # 无目标计数器
    fast_return_triggered = False  # 快速回位触发标志
    lock_stable_count = 0   # 锁定稳定计数器
    
    # 串口初始化 - 仅用于舵机控制
    fm.register(0, fm.fpioa.UART1_RX, force=True)
    fm.register(1, fm.fpioa.UART1_TX, force=True)
    uart = UART(UART.UART1, 115200, read_buf_len=4096)
    
    # 舵机回中位
    func_servo(ID1, servo_x_center, 1000)
    func_servo(ID2, servo_y_center, 1000)
    time.sleep(1)
    
    print("第二问+第三问联合系统启动...")
    print("流程：第二问手动瞄准锁定 → 记录位置 → 第三问移动云台后快速归位")
    
    # 主循环 - 第二问+第三问联合系统
    while True:
        try:
            gc.collect()
            clock.tick()

            # 图像采集
            img = sensor.snapshot()
            if img is None:
                continue

            # 图像预处理 - 边缘增强
            img.laplacian(1, sharpen=True)

            # 矩形检测
            rectangles = img.find_rects(threshold=25000)

            # 处理检测到的矩形
            if rectangles:
                # 重置无目标计数器
                no_target_count = 0
                fast_return_triggered = False

                # 只处理第一个矩形
                r = rectangles[0]
                # 绘制检测结果
                img.draw_rectangle(r.rect(), color=(255, 0, 0))

                # 计算矩形中心
                corners = r.corners()
                center_x = (corners[0][0] + corners[1][0] + corners[2][0] + corners[3][0]) / 4
                center_y = (corners[0][1] + corners[1][1] + corners[2][1] + corners[3][1]) / 4

                # PID控制计算
                error_x = center_x - target_x
                error_y = center_y - target_y

                # 积分项更新
                if abs(error_x) > 50 or abs(error_y) > 50:
                    integral_x = 0
                    integral_y = 0
                    lock_stable_count = 0
                else:
                    integral_x = error_x * ki_x + integral_x
                    integral_y = error_y * ki_y + integral_y

                # 积分限幅
                integral_x = max(-200, min(200, integral_x))
                integral_y = max(-200, min(200, integral_y))

                # PID输出
                output_x = integral_x
                output_y = integral_y

                # 智能死区控制
                error_magnitude = sqrt(error_x*error_x + error_y*error_y)

                if error_magnitude < 8:
                    output_x *= 0.1
                    output_y *= 0.1
                    integral_x *= 0.7
                    integral_y *= 0.7
                    lock_stable_count += 1
                elif error_magnitude < 15:
                    output_x *= 0.4
                    output_y *= 0.4
                    lock_stable_count = 0
                else:
                    lock_stable_count = 0

                # 输出限制
                max_output = 300
                output_x = max(-max_output, min(max_output, output_x))
                output_y = max(-max_output, min(max_output, output_y))

                # 舵机位置计算
                servo_x_pos = servo_x_center - int(output_x)
                servo_y_pos = servo_y_center + int(output_y)

                # 舵机位置限制
                servo_x_pos = max(1000, min(3000, servo_x_pos))
                servo_y_pos = max(1000, min(3000, servo_y_pos))

                # 舵机控制
                func_servo(ID1, servo_x_pos, interval)
                func_servo(ID2, servo_y_pos, interval)

                # 位置记录
                if error_magnitude < 8 and lock_stable_count > 5:
                    target_locked_x = servo_x_pos
                    target_locked_y = servo_y_pos

            else:
                # 无目标时的处理
                no_target_count += 1

                # 第三问模式：快速回位功能
                if no_target_count > 3 and target_locked_x is not None and not fast_return_triggered:
                    # 快速移动到记录位置
                    func_servo(ID1, target_locked_x, 150)
                    func_servo(ID2, target_locked_y, 150)
                    fast_return_triggered = True

            # 简化显示
            img.draw_cross(target_x, target_y, color=(255, 255, 0), scale=4)

            # 简单状态显示
            if target_locked_x is not None:
                img.draw_string(0, 0, "SAVED", color=(0, 255, 0), scale=2)
            else:
                img.draw_string(0, 0, "TRACKING", color=(255, 255, 0), scale=2)

            # LCD显示
            lcd.display(img)

        except Exception as e:
            # 异常处理，防止卡死
            print("Error:", e)
            time.sleep(0.1)
            continue

# 程序入口
if __name__ == "__main__":
    main()
