# 野火K210适配版本 - 基于01Studio代码移植
# 适配野火K210 AI视觉相机硬件平台

from machine import Timer, PWM, UART
import time
from math import sqrt, pi
import math
import sensor, image, lcd
from fpioa_manager import fm
from board import board_info
import gc

# 硬件初始化配置 - 严格按照01Studio原版逻辑
def hardware_init():
    """野火K210硬件初始化 - 保持与01Studio完全一致"""

    # 摄像头初始化 - 完全按照01Studio原版
    sensor.reset()  # 简单复位，不使用dual_buff参数
    sensor.set_pixformat(sensor.GRAYSCALE)  # 灰度模式
    sensor.set_framesize(sensor.QQVGA)      # QQVGA分辨率
    sensor.set_brightness(-2)
    sensor.set_saturation(-2)
    sensor.skip_frames(time=2000)

    # LCD初始化 - 野火K210标准配置
    lcd.init()
    lcd.clear(lcd.WHITE)

    print("野火K210硬件初始化完成")

# 舵机控制函数 - 适配野火K210引脚
def func_servo(id0, posit0, interval0):
    """舵机控制函数 - 使用野火K210串口协议"""
    ZT1 = 0xFF
    ZT2 = 0xFF
    DATA1 = 0X2A
    DATA2 = (posit0 >> 8) & 0xff
    DATA3 = posit0 & 0xff
    DATA4 = (interval0 >> 8) & 0xff
    DATA5 = interval0 & 0xff
    data_length = 0x07
    WriteDATA = 0X03
    GetChecksum = (~(id0 + data_length + WriteDATA + DATA1 + DATA2 + DATA3 + DATA4 + DATA5)) & 0xff
    text = bytes([ZT1, ZT2, id0, data_length, WriteDATA, DATA1, DATA2, DATA3, DATA4, DATA5, GetChecksum])
    uart.write(text)

# 主程序配置
def main():
    """主程序入口"""
    global uart, ZX, ZY, xin, yin, P, P2, I, I2, PI, PI2
    global state, text2, text2_last, condition, black
    global intiposit, posit, positx, interval, ID1, ID2, theta

    # 硬件初始化
    hardware_init()

    # 变量初始化
    clock = time.clock()
    ZX = 70
    xin = 80
    ZY = 70
    yin = 60
    P = 15
    P2 = 15
    I = 0.1
    I2 = 0.1
    PI = 0
    PI2 = 0
    kd = 0.1
    state = 0
    text2 = 0
    text2_last = 0
    condition = 1
    black = (150, 255)
    pre_error = 0
    # 舵机参数初始化
    intiposit = 2096
    posit = 2025
    positx = 2048
    interval = 50
    ID1 = 0x01
    ID2 = 0x02
    theta = 0

    # 串口引脚映射 - 根据野火K210例程标准配置
    # 使用野火K210标准引脚：引脚0(TX)、引脚1(RX)
    fm.register(0, fm.fpioa.UART1_RX, force=True)   # 野火K210标准RX引脚
    fm.register(1, fm.fpioa.UART1_TX, force=True)   # 野火K210标准TX引脚

    # 初始化串口 - 野火K210配置
    uart = UART(UART.UART1, 115200, read_buf_len=4096)

    # 舵机软启动模式 - 避免突然跳转
    print("舵机软启动模式：请在5秒内手动调整摄像头对准靶心...")
    print("5秒后舵机将缓慢接管控制...")

    # 等待5秒，让用户手动调整
    for i in range(5):
        print(f"倒计时: {5-i}秒")
        time.sleep(1)

    # 舵机缓慢接管 - 从当前位置开始
    print("舵机开始接管控制...")

    print("野火K210系统初始化完成，开始主循环...")

    # 软启动标志
    soft_start_frames = 30  # 前30帧使用软启动
    frame_count = 0

    # 主循环
    while True:
        # 垃圾回收
        #gc.collect()

        # 时钟更新
        clock.tick()

        # 帧计数
        frame_count += 1

        # 图像采集
        img = sensor.snapshot()

        # 串口数据读取
        text = uart.read(1)
        if text:
            try:
                text2 = text.decode('utf-8')
            except:
                text2 = 0

        # 图像处理 - 拉普拉斯边缘检测
        img.laplacian(1, sharpen=True)

        # 矩形检测
        for r in img.find_rects(threshold=30000):
            img.draw_rectangle(r.rect(), color=(255, 0, 0))
            for p in r.corners():
                img.draw_circle(p[0], p[1], 5, color=(0, 255, 0))

            # 计算矩形中心
            cor = r.corners()
            ZX = (cor[0][0] + cor[1][0] + cor[2][0] + cor[3][0]) / 4
            ZY = (cor[0][1] + cor[1][1] + cor[2][1] + cor[3][1]) / 4

        # 自适应PID控制计算
        error_x = ZX - xin
        error_y = ZY - yin

        # 根据误差大小动态调整PID参数
        error_magnitude = sqrt(error_x*error_x + error_y*error_y)

        if error_magnitude > 25:  # 超大误差：极速模式
            kp_x, kp_y = 8, 5  # 极高比例增益
            ki_x, ki_y = 0.3, 0.3  # 低积分增益避免超调
            interval_dynamic = 10  # 极速舵机响应
        elif error_magnitude > 15:  # 大误差：快速响应模式
            kp_x, kp_y = 6, 4  # 高比例增益
            ki_x, ki_y = 0.5, 0.5  # 中等积分增益
            interval_dynamic = 15  # 快速舵机响应
        elif error_magnitude > 8:  # 中等误差：平衡模式
            kp_x, kp_y = 4, 3  # 中等比例增益
            ki_x, ki_y = 0.8, 0.8  # 中等积分增益
            interval_dynamic = 25  # 中等舵机响应
        else:  # 小误差：精确模式（一环精度）
            kp_x, kp_y = 2, 1  # 原始比例增益
            ki_x, ki_y = 1, 1  # 原始积分增益
            interval_dynamic = 40  # 精确响应（从50减到40）

        # PID计算
        P = error_x * kp_x
        P2 = error_y * kp_y
        I = error_x * ki_x + I
        I2 = error_y * ki_y + I2
        PI = P + I
        PI2 = P2 + I2
        D2 = (error_y - pre_error) * kd
        pre_error = error_y
        PID2 = PI2 + D2


        # 限制积分项的范围，避免积分饱和
        I = min(max(I, -200), 200)  # 限制I的最大值
        I2 = min(max(I2, -50), 50)  # 限制I2的最大值

        # 智能死区控制（一环精度）
        if error_magnitude < 5:  # 一环精度死区（放宽到5像素）
            PI = PI * 0.2    # 在死区内减少输出（从0.1改为0.2，允许更快稳定）
            PI2 = PI2 * 0.2
            I = I * 0.85     # 减少积分项（从0.9改为0.85，更快收敛）
            I2 = I2 * 0.85
        elif error_magnitude < 8:  # 微调区域
            PI = PI * 0.6    # 中等减少输出（从0.5改为0.6，更积极）
            PI2 = PI2 * 0.6
            I = I * 0.9      # 轻微减少积分项
            I2 = I2 * 0.9
        # 周期性运动
        theta = theta + 0.001
        if theta >= 2 * pi:
            theta = 0

        # 串口指令处理
        if text2 != text2_last:
            state = 1
        text2_last = text2

        if state == 1:
            if text2 == '2':  # 回中位
                positx = 2048
                PI = 0
                PI2 = 0
                func_servo(ID1, int(positx - PI), interval)
                func_servo(ID2, int(posit - PI2), interval)
                time.sleep(1)
                state = 0
            elif text2 == '3':  # 左转
                positx = 1000
                PI = 0
                PI2 = 0
                func_servo(ID1, int(positx - PI), interval)
                func_servo(ID2, int(posit - PI2), interval)
                time.sleep(1)
                state = 0
            elif text2 == '4':  # 回中位
                positx = 2048
                PI = 0
                PI2 = 0
                func_servo(ID1, int(positx - PI), interval)
                func_servo(ID2, int(posit - PI2), interval)
                time.sleep(1)
                state = 0
            elif text2 == '5':  # 右转
                positx = 3000
                PI = 0
                PI2 = 0
                func_servo(ID1, int(positx - PI), interval)
                func_servo(ID2, int(posit - PI2), interval)
                time.sleep(1)
                state = 0

        # 软启动舵机控制
        if frame_count <= soft_start_frames:
            # 软启动阶段：PID输出逐渐增加，避免突然跳转
            soft_factor = frame_count / soft_start_frames  # 0到1的渐变系数
            PI_soft = PI * soft_factor
            PI2_soft = PI2 * soft_factor

            # 显示软启动状态
            img.draw_string(0, 50, f"Soft Start: {int(soft_factor*100)}%", color=(255, 255, 0), scale=2)

            func_servo(ID1, int(positx - PI_soft), interval_dynamic)
            func_servo(ID2, int(posit + PI2_soft), interval_dynamic)
        else:
            # 正常控制阶段
            func_servo(ID1, int(positx - PI), interval_dynamic)
            func_servo(ID2, int(posit + PI2), interval_dynamic)

        # 显示FPS和状态信息
        fps = clock.fps()
        img.draw_string(0, 0, "FPS: %2.1f" % fps, color=(255, 0, 0), scale=1)

        # 显示控制模式状态
        if error_magnitude > 25:
            img.draw_string(0, 15, "Mode: TURBO", color=(255, 0, 255), scale=2)
        elif error_magnitude > 15:
            img.draw_string(0, 15, "Mode: FAST", color=(255, 0, 0), scale=2)
        elif error_magnitude > 8:
            img.draw_string(0, 15, "Mode: BALANCE", color=(255, 255, 0), scale=2)
        elif error_magnitude < 5:
            img.draw_string(0, 15, "Mode: LOCKED", color=(0, 255, 0), scale=2)
        else:
            img.draw_string(0, 15, "Mode: PRECISE", color=(0, 255, 255), scale=2)

        # 显示误差信息
        img.draw_string(0, 35, "Error: %.1f" % error_magnitude, color=(255, 255, 255), scale=1)

        # 绘制中心十字线
        img.draw_cross(80, 60, color=(255, 0, 0), scale=4)

        # LCD显示
        lcd.display(img)

# 程序入口
if __name__ == "__main__":
    main()
