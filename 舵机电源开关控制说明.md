# 舵机电源开关控制版本

## 系统功能

### 核心原理
**通过舵机电源开关的开/关状态自动检测舵机上电，无需外部信号**

### 硬件要求
- K210摄像头模块
- 舵机云台（带电源开关）
- 串口连接线（K210与舵机通信）

## 工作流程

### 第二问：手动调整 + 自动锁定
1. **K210上电**：显示准星，舵机电源关闭
2. **打开舵机电源**：系统检测到舵机上电
3. **手动调整云台**：让准星对准靶心
4. **自动锁定**：准星接近靶心30帧后自动锁定并保存位置

### 第三问：断电重新上电归位
1. **关闭舵机电源**：舵机断电
2. **评委改变位置**：移动整个设备
3. **重新打开舵机电源**：系统检测到舵机重新上电
4. **自动归位**：舵机自动移动到第二问记录的位置

## 详细操作步骤

### 第二问操作

#### 步骤1：系统启动
```
1. 上传main_final代码到K210
2. K210上电，显示"Q2: MANUAL"
3. 舵机电源保持关闭状态
```

#### 步骤2：舵机上电
```
1. 打开舵机电源开关
2. 系统检测到舵机上电
3. 显示"Q2: READY"
4. 舵机状态显示"ONLINE"
```

#### 步骤3：手动调整
```
1. 手动调整云台
2. 让黄色准星对准红色矩形靶心
3. 准星接近靶心时开始计数"Locking: X/30"
```

#### 步骤4：自动锁定
```
1. 准星稳定在靶心附近30帧（约1秒）
2. 系统自动计算并保存位置
3. 舵机移动到锁定位置
4. 显示"Q2: LOCKED"，第二问完成
```

### 第三问操作

#### 步骤1：舵机断电
```
1. 关闭舵机电源开关
2. 舵机失去动力
3. 舵机状态显示"OFFLINE"
```

#### 步骤2：改变位置
```
1. 评委任意移动整个设备
2. 改变云台位置和方向
3. K210持续显示画面（不断电）
```

#### 步骤3：舵机重新上电
```
1. 重新打开舵机电源开关
2. 系统检测到舵机重新上电
3. 自动读取保存的位置
```

#### 步骤4：自动归位
```
1. 舵机自动移动到记录位置
2. 准星重新回到靶心区域
3. 显示"Q3: RETURNED"
4. 第三问完成
```

## 屏幕状态说明

| 显示内容 | 含义 | 操作 |
|---------|------|------|
| `Q2: MANUAL` | 第二问手动模式 | 打开舵机电源 |
| `Q2: READY` | 舵机已上电，等待调整 | 手动调整准星对准靶心 |
| `Locking: X/30` | 正在锁定计数 | 保持准星稳定 |
| `Q2: LOCKED` | 第二问完成 | 位置已保存，可测试第三问 |
| `Q3: RETURNED` | 第三问归位完成 | 准星已回到靶心 |

## 舵机状态显示

| 状态 | 含义 | 说明 |
|------|------|------|
| `Servo: OFFLINE (0)` | 舵机离线 | 电源关闭或通信异常 |
| `Servo: ONLINE (1)` | 舵机第1次上电 | 第二问模式 |
| `Servo: ONLINE (2)` | 舵机第2次上电 | 第三问模式 |

## 技术原理

### 舵机状态检测
```python
# 每10帧检测一次舵机是否在线
if servo_check_count % 10 == 0:
    func_servo(ID1, servo_x_center, 0)  # 发送测试命令
    response = uart.read()
    servo_online = (response and len(response) > 0)
```

### 上电状态变化检测
```python
if servo_online and not last_servo_online:
    # 舵机从离线变为在线（上电）
    servo_power_count += 1
    触发相应功能
```

### 自动锁定逻辑
```python
if error_magnitude < 15:  # 准星接近靶心
    stable_lock_count += 1
    if stable_lock_count > 30:  # 稳定30帧
        自动锁定并保存位置
```

## 关键参数

```python
# 检测参数
threshold = 25000          # 矩形检测阈值
lock_distance = 15         # 锁定距离阈值（像素）
stable_frames = 30         # 稳定帧数要求

# 舵机参数
servo_center = 2048        # 舵机中心位置
position_scale = 10        # 位置计算放大系数
lock_time = 500           # 锁定移动时间（毫秒）
return_time = 300         # 归位移动时间（毫秒）
```

## 优势特点

### 1. 硬件简单
- 只需K210和舵机云台
- 通过电源开关控制
- 无需额外控制设备

### 2. 操作直观
- 电源开关直接控制功能
- 屏幕状态清晰显示
- 自动检测舵机状态

### 3. 功能完整
- 自动检测舵机上电
- 手动调整+自动锁定
- 断电重新上电归位

### 4. 可靠性高
- 基于硬件状态检测
- 多重状态验证
- 异常处理完善

## 故障排除

### 问题1：检测不到舵机上电
- **原因**：串口连接问题或舵机故障
- **解决**：检查串口连接，确认舵机正常工作

### 问题2：不自动锁定
- **原因**：准星未稳定对准靶心
- **解决**：手动精确调整，保持准星在靶心附近

### 问题3：锁定位置不准确
- **原因**：手动调整时准星偏离靶心
- **解决**：重新精确调整后再等待自动锁定

### 问题4：第三问不归位
- **原因**：第二问未正确完成或文件丢失
- **解决**：确认第二问显示"Q2: LOCKED"

## 测试验证

### 完整测试流程
1. **第二问验证**：
   - K210上电 → 打开舵机电源 → 手动调整 → 自动锁定
   - 确认显示"Q2: LOCKED"

2. **第三问验证**：
   - 关闭舵机电源 → 移动设备 → 重新打开电源 → 自动归位
   - 确认显示"Q3: RETURNED"

### 性能指标
- **舵机检测响应**：1秒内检测到状态变化
- **自动锁定时间**：准星稳定后1秒内锁定
- **归位响应时间**：舵机上电后0.3秒内开始归位
- **位置精度**：基于视觉计算的高精度位置

**这个版本完全基于舵机电源开关控制，无需任何外部信号，完全符合您的硬件条件！**
