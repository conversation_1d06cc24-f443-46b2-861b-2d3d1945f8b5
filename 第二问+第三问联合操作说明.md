# 第二问+第三问联合系统操作说明

## 系统概述
完整的第二问精确追踪 + 第三问快速归位联合系统，实现无缝衔接的比赛流程。

## 完整操作流程

### 第一步：启动系统（第二问模式）
1. 上传`main_stable`代码到K210
2. 系统自动启动，默认进入**第二问模式**
3. 屏幕显示：`Mode: Q2 | FPS: xx.x`

### 第二步：第二问 - 手动瞄准和锁定
1. **手动调整**：手动调整云台，让摄像头瞄准靶心
2. **自动追踪**：系统检测到矩形后自动开始精确追踪
3. **稳定锁定**：当误差小于8像素且稳定5帧后
4. **位置记录**：屏幕显示`Q2: LOCKED & SAVED`和具体坐标
5. **完成标志**：显示`Ready for Q3!`

### 第三步：切换到第三问模式
**方法一：自动切换**
- 系统检测到稳定锁定后，可以手动移动云台触发第三问

**方法二：串口切换（可选）**
- 发送字符'3'到串口切换到第三问模式
- 屏幕显示：`Mode: Q3 | FPS: xx.x`

### 第四步：第三问 - 移动云台测试归位
1. **移动云台**：评委任意移动云台位置和方向
2. **丢失检测**：系统检测到目标丢失
3. **快速归位**：3帧后（约0.1秒）自动触发快速归位
4. **归位完成**：150ms内移动到记录位置
5. **重新锁定**：到达位置后重新检测和锁定目标

## 屏幕状态显示

### 第二问状态
| 显示内容 | 含义 |
|---------|------|
| `Q2: TRACKING` | 正在追踪目标 |
| `Q2: FINE TUNE` | 精细调整中 |
| `Q2: LOCKING...` | 正在稳定锁定（显示进度x/5） |
| `Q2: LOCKED & SAVED` | 已锁定并保存位置 |
| `Ready for Q3!` | 准备进入第三问 |

### 第三问状态
| 显示内容 | 含义 |
|---------|------|
| `Q3: NO TARGET` | 未检测到目标 |
| `Q3: FAST RETURN!` | 正在快速归位 |
| `To: xxxx,yyyy` | 归位目标坐标 |

### 通用状态
| 显示内容 | 含义 |
|---------|------|
| `Mode: Q2` | 当前第二问模式 |
| `Mode: Q3` | 当前第三问模式 |
| `Saved: Xxxxx Yyyyy` | 已保存的位置坐标 |
| `No position saved` | 尚未保存位置 |

## 关键参数

```python
# 模式控制
question_mode = 2          # 2=第二问，3=第三问

# 第二问参数
lock_stable_count = 5      # 需要稳定5帧才记录位置
error_threshold = 8        # 锁定误差阈值

# 第三问参数
no_target_trigger = 3      # 丢失3帧触发归位
fast_return_time = 150     # 快速移动时间150ms
detection_threshold = 25000 # 第三问检测阈值（更灵敏）
```

## 比赛实战流程

### 准备阶段
1. 确保K210和舵机连接正常
2. 上传联合系统代码
3. 检查串口通信（115200波特率）

### 第二问执行
1. 系统启动后自动进入第二问模式
2. 手动粗调云台对准靶心
3. 等待系统自动精确追踪
4. 观察屏幕显示`Q2: LOCKED & SAVED`
5. 确认坐标已保存（绿色显示）

### 第三问执行
1. 评委移动云台到任意位置
2. 系统自动检测目标丢失
3. 3帧后触发快速归位
4. 150ms内移动到记录位置
5. 重新检测并锁定目标

## 故障排除

### 问题1：第二问无法锁定
- **检查**：目标是否在视野内
- **解决**：手动调整云台，确保矩形清晰可见

### 问题2：位置无法保存
- **检查**：是否稳定锁定5帧以上
- **解决**：等待误差小于8像素且持续稳定

### 问题3：第三问归位不准确
- **检查**：第二问是否正确保存位置
- **解决**：重新执行第二问锁定流程

### 问题4：模式切换问题
- **检查**：串口连接是否正常
- **解决**：系统会自动处理模式切换

## 性能指标

- **第二问锁定精度**：误差小于8像素
- **位置记录精度**：舵机坐标精确到1个单位
- **第三问响应时间**：3帧检测（约0.1秒）
- **归位移动时间**：150ms
- **总归位时间**：约0.25秒（远低于4秒要求）

## 技术优势

1. **无缝衔接**：第二问和第三问自然过渡
2. **自动记录**：稳定锁定后自动保存位置
3. **快速响应**：极短的检测和归位时间
4. **可靠性高**：多重状态检查和错误处理
5. **实时反馈**：丰富的状态显示便于监控

**您的联合方案已完美实现，可以开始完整测试了！**
