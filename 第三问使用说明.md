# 第三问快速定位系统使用说明

## 系统概述
基于您的思路实现的智能位置记录和快速回位系统，专门解决第三问的4秒定位要求。

## 核心功能

### 1. 智能位置记录
- **触发条件**：当误差小于8像素且稳定锁定5帧以上
- **记录内容**：精确的舵机X、Y轴位置坐标
- **显示状态**：屏幕显示"LOCKED & SAVED"和具体坐标

### 2. 快速回位机制
- **触发条件**：丢失目标3帧后（约0.1秒）
- **回位速度**：150ms快速移动
- **显示状态**：屏幕显示"FAST RETURN!"

### 3. 优化改进
- **检测阈值降低**：从30000降至25000，提高检测灵敏度
- **稳定性检查**：需要稳定锁定5帧才记录位置，避免误记录
- **快速响应**：仅3帧丢失就触发回位，确保4秒内完成

## 文件说明

### main_stable（改进版）
- 在原有追踪功能基础上添加位置记录
- 保持原有的精确追踪性能
- 适合需要高精度追踪的场景

### main_question3（专用版）
- 专门为第三问优化的版本
- 更快的响应速度
- 更清晰的状态显示
- 推荐用于比赛

## 使用流程

### 第一步：系统初始化
1. 上传代码到K210
2. 系统自动初始化摄像头和舵机
3. 舵机回到中心位置

### 第二步：位置记录
1. 手动调整或让系统自动追踪到靶心
2. 当屏幕显示"LOCKED & SAVED"时，位置已记录
3. 记录的坐标会显示在屏幕上

### 第三步：快速定位测试
1. 评委改变摄像头位置/方向
2. 系统检测到目标丢失
3. 3帧后自动触发快速回位
4. 150ms内移动到记录位置

## 关键参数

```python
# 检测参数
threshold = 25000          # 矩形检测阈值（降低提高灵敏度）

# 位置记录参数
lock_stable_count = 5      # 稳定锁定帧数要求
error_threshold = 8        # 锁定误差阈值（像素）

# 快速回位参数
no_target_trigger = 3      # 丢失目标触发帧数
fast_return_time = 150     # 快速移动时间（毫秒）
```

## 状态显示说明

| 显示内容 | 含义 | 颜色 |
|---------|------|------|
| LOCKED & SAVED | 已锁定并保存位置 | 绿色 |
| LOCKING... | 正在稳定锁定中 | 黄色 |
| TRACKING | 正在追踪目标 | 青色 |
| NO TARGET | 未检测到目标 | 红色 |
| FAST RETURN! | 正在快速回位 | 紫色 |

## 性能指标

- **位置记录精度**：舵机坐标精确到1个单位
- **快速回位时间**：150ms移动时间
- **检测响应时间**：3帧检测丢失（约0.1秒）
- **总定位时间**：理论上0.25秒内完成定位

## 故障排除

### 问题1：位置无法保存
- **原因**：目标不够稳定
- **解决**：确保误差小于8像素且持续5帧以上

### 问题2：快速回位不准确
- **原因**：记录位置时环境已变化
- **解决**：在稳定环境下重新记录位置

### 问题3：检测不到目标
- **原因**：光线变化或阈值过高
- **解决**：调整threshold参数或改善光线条件

## 比赛建议

1. **赛前准备**：在比赛环境下先完成一次完整的锁定和位置记录
2. **环境适应**：根据现场光线条件微调检测阈值
3. **备用方案**：如果快速回位失效，系统会显示提示信息
4. **时间控制**：整个定位过程设计在1秒内完成，远低于4秒要求

## 技术优势

1. **简单可靠**：基于您的核心思路，逻辑简单不易出错
2. **响应迅速**：3帧检测+150ms移动，总时间极短
3. **精度保证**：记录舵机精确坐标，定位准确
4. **容错能力**：多重状态检查，避免误操作
5. **实时反馈**：丰富的屏幕显示，便于调试和监控

您的方案确实是最优解！
