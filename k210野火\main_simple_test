# 最简化测试版本 - 防卡死
# 第二问+第三问联合系统

from machine import UART
import time
import sensor, image, lcd
from fpioa_manager import fm
import gc
from math import sqrt

def func_servo(id0, posit0, interval0):
    """舵机控制函数"""
    try:
        ZT1 = 0xFF
        ZT2 = 0xFF
        DATA1 = 0X2A
        DATA2 = (posit0 >> 8) & 0xff
        DATA3 = posit0 & 0xff
        DATA4 = (interval0 >> 8) & 0xff
        DATA5 = interval0 & 0xff
        data_length = 0x07
        WriteDATA = 0X03
        GetChecksum = (~(id0 + data_length + WriteDATA + DATA1 + DATA2 + DATA3 + DATA4 + DATA5)) & 0xff
        text = bytes([ZT1, ZT2, id0, data_length, WriteDATA, DATA1, DATA2, DATA3, DATA4, DATA5, GetChecksum])
        uart.write(text)
    except:
        pass

def main():
    """主程序"""
    global uart
    
    # 硬件初始化
    try:
        sensor.reset()
        sensor.set_pixformat(sensor.GRAYSCALE)
        sensor.set_framesize(sensor.QQVGA)
        sensor.set_brightness(-2)
        sensor.set_saturation(-2)
        sensor.skip_frames(time=2000)
        
        lcd.init()
        lcd.clear(lcd.WHITE)
        print("硬件初始化完成")
    except Exception as e:
        print("硬件初始化失败:", e)
        return
    
    # 参数初始化
    clock = time.clock()
    target_x = 70
    target_y = 70
    
    # PID参数
    ki_x = 1
    ki_y = 1
    integral_x = 0
    integral_y = 0
    
    # 舵机参数
    servo_x_center = 2048
    servo_y_center = 2048
    servo_x_pos = servo_x_center
    servo_y_pos = servo_y_center
    interval = 100
    ID1 = 0x01
    ID2 = 0x02
    
    # 功能变量
    target_locked_x = None
    target_locked_y = None
    no_target_count = 0
    fast_return_triggered = False
    lock_stable_count = 0

    # 模式控制
    servo_enabled = False  # 舵机是否上电控制
    position_saved = False  # 位置是否已保存
    servo_power_count = 0  # 舵机上电计数器
    last_servo_state = False  # 上次舵机状态
    
    # 串口初始化
    try:
        fm.register(0, fm.fpioa.UART1_RX, force=True)
        fm.register(1, fm.fpioa.UART1_TX, force=True)
        uart = UART(UART.UART1, 115200, read_buf_len=4096)
        
        # 尝试读取保存的位置
        try:
            with open('/flash/saved_position.txt', 'r') as f:
                data = f.read().strip().split(',')
                target_locked_x = int(data[0])
                target_locked_y = int(data[1])
                print("发现保存位置: X=%d, Y=%d" % (target_locked_x, target_locked_y))
        except:
            print("未发现保存位置")

        # 默认进入等待模式
        mode = "WAIT"
        print("系统启动完成 - 等待舵机上电信号")
    except Exception as e:
        print("串口初始化失败:", e)
        return
    
    # 主循环
    while True:
        try:
            # 基础操作
            gc.collect()
            clock.tick()

            # 图像采集
            img = sensor.snapshot()
            if img is None:
                continue

            # 简化处理，自动启动舵机
            if not servo_enabled:
                servo_enabled = True
                if target_locked_x is not None:
                    print("系统启动 - 有保存位置，准备自动归位")
                else:
                    print("系统启动 - 无保存位置，进入追踪模式")
                    mode = "Q2"

            # 图像处理
            img.laplacian(1, sharpen=True)

            # 矩形检测
            rectangles = img.find_rects(threshold=25000)
            
            if rectangles:
                # 检测到矩形目标
                no_target_count = 0
                fast_return_triggered = False

                # 处理第一个矩形
                r = rectangles[0]
                img.draw_rectangle(r.rect(), color=(255, 0, 0))

                # 计算中心
                corners = r.corners()
                center_x = sum(p[0] for p in corners) / 4
                center_y = sum(p[1] for p in corners) / 4

                # PID控制
                error_x = center_x - target_x
                error_y = center_y - target_y
                error_magnitude = sqrt(error_x*error_x + error_y*error_y)

                # 积分更新
                if abs(error_x) > 50 or abs(error_y) > 50:
                    integral_x = 0
                    integral_y = 0
                    lock_stable_count = 0
                else:
                    integral_x = error_x + integral_x
                    integral_y = error_y + integral_y

                # 积分限幅
                integral_x = max(-200, min(200, integral_x))
                integral_y = max(-200, min(200, integral_y))

                # 输出计算
                output_x = integral_x
                output_y = integral_y

                # 死区控制
                if error_magnitude < 8:
                    output_x *= 0.1
                    output_y *= 0.1
                    lock_stable_count += 1
                else:
                    lock_stable_count = 0

                # 输出限制
                output_x = max(-300, min(300, output_x))
                output_y = max(-300, min(300, output_y))

                # 舵机控制
                servo_x_pos = servo_x_center - int(output_x)
                servo_y_pos = servo_y_center + int(output_y)
                servo_x_pos = max(1000, min(3000, servo_x_pos))
                servo_y_pos = max(1000, min(3000, servo_y_pos))

                func_servo(ID1, servo_x_pos, interval)
                func_servo(ID2, servo_y_pos, interval)

                # 位置记录和保存
                if error_magnitude < 8 and lock_stable_count > 10 and not position_saved:
                    target_locked_x = servo_x_pos
                    target_locked_y = servo_y_pos

                    # 保存到文件
                    try:
                        with open('/flash/saved_position.txt', 'w') as f:
                            f.write("%d,%d" % (target_locked_x, target_locked_y))
                        position_saved = True
                        print("位置已保存: X=%d, Y=%d" % (target_locked_x, target_locked_y))
                    except:
                        print("位置保存失败")

            else:
                # 检测不到矩形目标
                no_target_count += 1

                # 当检测不到目标且有保存位置时，自动复位
                if no_target_count > 5 and target_locked_x is not None and not fast_return_triggered:
                    print("检测不到靶子，自动复位到保存位置")
                    func_servo(ID1, target_locked_x, 200)  # 快速复位
                    func_servo(ID2, target_locked_y, 200)
                    fast_return_triggered = True
                    print("复位完成，准星应该回到矩形框内")
                
            # 显示
            img.draw_cross(target_x, target_y, color=(255, 255, 0), scale=4)

            # 状态显示
            if rectangles:
                if position_saved:
                    img.draw_string(0, 0, "SAVED", color=(0, 255, 0), scale=2)
                else:
                    img.draw_string(0, 0, "TRACKING", color=(0, 255, 255), scale=2)
            else:
                if target_locked_x is not None:
                    if fast_return_triggered:
                        img.draw_string(0, 0, "RESET DONE", color=(0, 255, 0), scale=2)
                    else:
                        img.draw_string(0, 0, "NO TARGET", color=(255, 0, 0), scale=2)
                        img.draw_string(0, 20, "Count: %d" % no_target_count, color=(255, 255, 0), scale=1)
                else:
                    img.draw_string(0, 0, "NO SAVE", color=(255, 255, 0), scale=2)

            lcd.display(img)
            
        except Exception as e:
            print("循环错误:", e)
            time.sleep(0.1)
            continue

if __name__ == "__main__":
    main()
