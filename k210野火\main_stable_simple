# 极简稳定版本 - 避免卡死
from machine import UART
import time
import sensor, image, lcd
from fpioa_manager import fm
import gc
from math import sqrt

def func_servo(id0, posit0, interval0):
    try:
        ZT1 = 0xFF
        ZT2 = 0xFF
        DATA1 = 0X2A
        DATA2 = (posit0 >> 8) & 0xff
        DATA3 = posit0 & 0xff
        DATA4 = (interval0 >> 8) & 0xff
        DATA5 = interval0 & 0xff
        data_length = 0x07
        WriteDATA = 0X03
        GetChecksum = (~(id0 + data_length + WriteDATA + DATA1 + DATA2 + DATA3 + DATA4 + DATA5)) & 0xff
        text = bytes([ZT1, ZT2, id0, data_length, WriteDATA, DATA1, DATA2, DATA3, DATA4, DATA5, GetChecksum])
        uart.write(text)
    except:
        pass

def main():
    global uart
    
    # 硬件初始化
    try:
        sensor.reset()
        sensor.set_pixformat(sensor.GRAYSCALE)
        sensor.set_framesize(sensor.QQVGA)
        sensor.set_brightness(-2)
        sensor.set_saturation(-2)
        sensor.skip_frames(time=2000)
        lcd.init()
        lcd.clear(lcd.WHITE)
    except:
        return
    
    # 参数初始化
    clock = time.clock()
    target_x = 70
    target_y = 70
    servo_x_center = 2048
    servo_y_center = 2048
    ID1 = 0x01
    ID2 = 0x02
    
    # 功能变量
    target_locked_x = None
    target_locked_y = None
    stable_lock_count = 0
    mode = "Q2_MANUAL"
    frame_count = 0
    no_target_count = 0  # 检测不到目标的计数
    position_saved = False  # 位置是否已保存
    reset_count = 0  # 重置计数器
    q3_triggered = False  # 第三问是否已触发
    
    # 串口初始化
    try:
        fm.register(0, fm.fpioa.UART1_RX, force=True)
        fm.register(1, fm.fpioa.UART1_TX, force=True)
        uart = UART(UART.UART1, 115200, read_buf_len=4096)
    except:
        return
    
    # 读取保存位置
    try:
        with open('/flash/saved_position.txt', 'r') as f:
            data = f.read().strip().split(',')
            target_locked_x = int(data[0])
            target_locked_y = int(data[1])
            position_saved = True
            mode = "Q2_LOCKED"  # 如果有保存位置，直接进入锁定状态
    except:
        position_saved = False
    
    # 主循环
    while True:
        try:
            gc.collect()
            clock.tick()
            frame_count += 1
            
            img = sensor.snapshot()
            if img is None:
                continue
            
            # 图像处理
            img.laplacian(1, sharpen=True)
            
            # 矩形检测
            rectangles = img.find_rects(threshold=25000)
            if rectangles:
                r = rectangles[0]
                img.draw_rectangle(r.rect(), color=(255, 0, 0))
                
                corners = r.corners()
                center_x = sum(p[0] for p in corners) / 4
                center_y = sum(p[1] for p in corners) / 4
                img.draw_circle(int(center_x), int(center_y), 5, color=(0, 255, 0))
                
                # 重置无目标计数
                no_target_count = 0
                reset_count = 0  # 重置重置计数

                # 第二问自动锁定逻辑
                if mode == "Q2_MANUAL" and target_locked_x is None:
                    error_x = center_x - target_x
                    error_y = center_y - target_y
                    error_magnitude = sqrt(error_x*error_x + error_y*error_y)

                    if error_magnitude < 15:
                        stable_lock_count += 1
                        if stable_lock_count > 50:  # 增加稳定帧数
                            target_locked_x = servo_x_center - int(error_x * 10)
                            target_locked_y = servo_y_center + int(error_y * 10)
                            target_locked_x = max(1000, min(3000, target_locked_x))
                            target_locked_y = max(1000, min(3000, target_locked_y))

                            try:
                                with open('/flash/saved_position.txt', 'w') as f:
                                    f.write("%d,%d" % (target_locked_x, target_locked_y))
                                func_servo(ID1, target_locked_x, 500)
                                func_servo(ID2, target_locked_y, 500)
                                mode = "Q2_LOCKED"
                                position_saved = True
                                q3_triggered = False  # 重置第三问标志
                            except:
                                pass
                    else:
                        stable_lock_count = 0

                # 第三问：重新检测到目标后归位
                elif mode == "Q3_RETURNED" and target_locked_x is not None:
                    # 第三问完成后，重新检测到目标，可以选择重新归位或保持
                    pass
            
            else:
                # 没有检测到矩形
                no_target_count += 1

                # 第三问：目标丢失超过一定时间，自动归位
                if mode == "Q2_LOCKED" and target_locked_x is not None and position_saved and not q3_triggered:
                    if no_target_count > 30:  # 连续30帧检测不到目标
                        func_servo(ID1, target_locked_x, 300)
                        func_servo(ID2, target_locked_y, 300)
                        mode = "Q3_RETURNED"
                        q3_triggered = True
                        no_target_count = 0

                # 支持重复测试：长时间无目标时重置系统
                if mode == "Q3_RETURNED":
                    reset_count += 1
                    if reset_count > 200:  # 200帧后可以重新开始
                        # 重置到第二问状态，但保留位置信息
                        mode = "Q2_LOCKED"
                        q3_triggered = False
                        reset_count = 0
                        no_target_count = 0
            
            # 显示准星
            img.draw_cross(target_x, target_y, color=(255, 255, 0), scale=4)
            
            # 状态显示
            if mode == "Q2_MANUAL":
                if target_locked_x is None:
                    img.draw_string(0, 0, "Q2: AIM", color=(255, 255, 0), scale=2)
                    if stable_lock_count > 0:
                        img.draw_string(0, 20, "Lock: %d/50" % stable_lock_count, color=(255, 255, 0), scale=1)
                else:
                    img.draw_string(0, 0, "Q2: READY", color=(0, 255, 0), scale=2)
            elif mode == "Q2_LOCKED":
                img.draw_string(0, 0, "Q2: LOCKED", color=(0, 255, 0), scale=2)
                if position_saved:
                    img.draw_string(0, 20, "POS: %d,%d" % (target_locked_x, target_locked_y), color=(0, 255, 0), scale=1)
                if no_target_count > 0:
                    img.draw_string(0, 40, "NoTarget: %d/30" % no_target_count, color=(255, 255, 0), scale=1)
            elif mode == "Q3_RETURNED":
                img.draw_string(0, 0, "Q3: RETURNED", color=(0, 255, 0), scale=2)
                img.draw_string(0, 20, "Reset: %d/200" % reset_count, color=(255, 255, 0), scale=1)

            # 添加重置提示
            if mode == "Q2_LOCKED" and target_locked_x is not None:
                img.draw_string(0, 60, "Move target to test Q3", color=(255, 255, 255), scale=1)
            
            lcd.display(img)
            
        except:
            time.sleep(0.1)
            continue

if __name__ == "__main__":
    main()
