# 正确的第二问+第三问操作说明

## 系统功能说明

### 第二问：手动调整 + 自动咬死 + 位置记录
1. **K210上电**：作为准星显示，舵机不动
2. **手动调整**：手动调整舵机云台，让K210准星对准靶心
3. **舵机上电**：发送'S'信号给K210，舵机开始自动控制
4. **自动咬死**：K210自动精确追踪，咬死靶心
5. **位置记录**：稳定锁定后自动保存位置到文件

### 第三问：舵机重新上电自动归位
1. **评委移动云台**：改变云台位置和方向，准星偏离靶心
2. **舵机重新上电**：发送'S'信号，舵机重新上电
3. **自动归位**：舵机自动移动到第二问记录的位置
4. **准星回归**：摄像头准星重新回到矩形框内

## 详细操作流程

### 第二问操作步骤

#### 步骤1：系统启动
```
上传代码 → K210上电 → 显示"Q2: MANUAL"
```

#### 步骤2：手动调整
- K210显示准星（黄色十字）
- **手动调整舵机云台**，让准星对准靶心
- 此时舵机不会自动移动

#### 步骤3：舵机上电
- 通过串口发送字符'S'
- 或者在代码中设置`servo_enabled = True`
- 屏幕显示变为"Q2: LOCKING"

#### 步骤4：自动咬死
- 系统开始自动PID控制
- 舵机自动微调，精确追踪靶心
- 准星逐渐锁定到靶心中央

#### 步骤5：位置保存
- 当误差小于8像素且稳定10帧后
- 自动保存位置到`/flash/saved_position.txt`
- 屏幕显示"Q2: SAVED"
- 控制台输出：`位置已保存: X=xxxx, Y=yyyy`

### 第三问操作步骤

#### 步骤1：评委移动云台
```
评委任意移动云台位置和方向 → 准星偏离靶心 → K210持续显示画面
```

#### 步骤2：舵机重新上电
- 发送'S'信号给K210
- 系统检测到有保存位置，自动进入"Q3"模式
- 显示"Q3: RETURNING"

#### 步骤3：自动归位
- 舵机自动移动到第二问保存的位置
- 移动时间约0.3秒
- 准星重新回到矩形框内

#### 步骤4：归位完成
- 显示"Q3: RETURNED"
- 准星成功回到靶心区域
- 可以继续进行精确追踪

## 屏幕状态说明

| 显示内容 | 含义 | 操作 |
|---------|------|------|
| `WAITING` | 等待舵机上电 | 发送'S'启动舵机 |
| `Q2: LOCKING` | 第二问自动锁定中 | 等待系统自动咬死靶心 |
| `Q2: SAVED` | 第二问位置已保存 | 第二问完成，可以测试第三问 |
| `Q3: RETURNING` | 第三问归位中 | 系统正在自动归位 |
| `Q3: RETURNED` | 第三问归位完成 | 准星已回到矩形框内 |

## 文件说明

### 位置保存文件
- **路径**：`/flash/saved_position.txt`
- **格式**：`X坐标,Y坐标`（例如：`2150,1980`）
- **作用**：记录第二问锁定时的舵机位置

### 舵机上电信号
- **方法1**：串口发送字符'S'
- **方法2**：代码中设置`servo_enabled = True`

## 测试流程

### 完整测试流程
1. **第二问测试**：
   - 上传代码，K210上电
   - 手动调整云台对准靶心
   - 发送'S'启动舵机自动控制
   - 等待显示"Q2: SAVED"

2. **第三问测试**：
   - 评委移动云台到任意位置
   - 舵机重新上电（发送'S'）
   - 观察是否自动归位到记录位置
   - 确认准星回到矩形框内，显示"Q3: RETURNED"

### 重置测试
如需重新测试，删除保存文件：
```python
import os
try:
    os.remove('/flash/saved_position.txt')
    print("位置文件已删除")
except:
    print("文件不存在")
```

## 关键参数

```python
# 锁定参数
error_threshold = 8        # 锁定误差阈值（像素）
stable_frames = 10         # 稳定帧数要求
save_delay = 0.5          # 保存延迟时间

# 归位参数
return_time = 500         # 归位移动时间（毫秒）
return_delay = 1.0        # 归位后等待时间（秒）
```

## 故障排除

### 问题1：第二问不保存位置
- **检查**：是否稳定锁定10帧以上
- **解决**：确保准星稳定在靶心中央

### 问题2：第三问不自动归位
- **检查**：是否存在位置保存文件
- **解决**：确认第二问已正确保存位置

### 问题3：舵机不响应
- **检查**：是否发送了'S'信号
- **解决**：确认串口通信正常

### 问题4：归位位置不准确
- **检查**：第二问保存时是否真正锁定
- **解决**：重新执行第二问，确保精确锁定后再保存

## 技术特点

1. **真正的手动+自动结合**：第二问先手动粗调，再自动精调
2. **持久化存储**：位置保存到Flash，断电不丢失
3. **智能模式切换**：根据是否有保存文件自动选择模式
4. **稳定性保证**：需要稳定锁定才保存，确保位置准确性

**现在的系统完全符合您的需求！**
