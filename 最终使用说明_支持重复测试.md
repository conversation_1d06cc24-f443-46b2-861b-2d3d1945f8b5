# 最终使用说明 - 支持重复测试版本

## 修复内容

### ✅ 问题1：支持重复测试
**原问题**：代码只能保存一次，每次测试都要调整代码
**解决方案**：
- 增加自动重置机制：第三问完成200帧后自动重置到Q2_LOCKED状态
- 提供手动重置工具：`reset_helper.py`
- 保留位置信息，支持多次第三问测试

### ✅ 问题2：第三问追踪修复
**原问题**：第三问给舵机上电后不能正确追踪到第二问位置
**解决方案**：
- 修复归位逻辑：确保舵机移动到正确的保存位置
- 增加状态标志：防止重复触发归位
- 显示具体位置：屏幕显示保存的坐标值

## 完整工作流程

### 第二问：手动调整 + 自动锁定
```
1. K210上电 → 显示"Q2: AIM"
2. 手动调整准星对准目标 → 显示"Lock: X/50"
3. 稳定50帧后自动锁定 → 显示"Q2: LOCKED"
4. 屏幕显示"POS: X,Y"确认位置已保存
```

### 第三问：目标丢失 + 自动归位
```
1. 移动设备或遮挡目标 → 显示"NoTarget: X/30"
2. 连续30帧无目标 → 舵机自动归位到保存位置
3. 显示"Q3: RETURNED" → 第三问完成
4. 200帧后自动重置 → 回到"Q2: LOCKED"状态
```

### 重复测试循环
```
Q2: LOCKED → 移动目标测试Q3 → Q3: RETURNED → 自动重置 → Q2: LOCKED
```

## 屏幕状态详解

### 第二问状态
| 显示内容 | 含义 | 操作 |
|---------|------|------|
| `Q2: AIM` | 瞄准模式 | 手动调整准星对准目标 |
| `Lock: X/50` | 锁定计数 | 保持准星稳定在目标上 |
| `Q2: LOCKED` | 锁定完成 | 第二问完成，可测试第三问 |
| `POS: X,Y` | 显示保存坐标 | 确认位置已正确保存 |

### 第三问状态
| 显示内容 | 含义 | 操作 |
|---------|------|------|
| `NoTarget: X/30` | 无目标计数 | 等待自动归位触发 |
| `Q3: RETURNED` | 归位完成 | 第三问完成 |
| `Reset: X/200` | 重置计数 | 等待自动重置到Q2状态 |
| `Move target to test Q3` | 测试提示 | 移动目标可重复测试第三问 |

## 测试方法

### 完整测试流程
```
1. 上传main_stable_simple代码
2. K210上电，手动调整完成第二问
3. 移动设备测试第三问归位
4. 等待自动重置，重复测试第三问
5. 如需重新测试第二问，使用重置工具
```

### 第二问测试
```
手动调整云台 → 准星对准红色矩形 → 
观察"Lock: X/50"计数 → 自动锁定 → 
确认显示"POS: X,Y"
```

### 第三问测试
```
方法1：遮挡摄像头
方法2：移动整个设备
方法3：改变光照条件
观察"NoTarget: X/30" → 自动归位 → "Q3: RETURNED"
```

### 重复测试
```
第三问完成后 → 等待"Reset: X/200"计数 → 
自动回到"Q2: LOCKED" → 可再次测试第三问
```

## 重置工具使用

### 快速重置（推荐）
```python
# 在K210上执行
exec(open('reset_helper.py').read())
reset_system()  # 删除保存位置，重新开始
```

### 检查当前状态
```python
exec(open('reset_helper.py').read())
check_saved_position()  # 查看保存的位置
```

### 手动设置位置（测试用）
```python
exec(open('reset_helper.py').read())
manual_save_position(2048, 2048)  # 设置中心位置
```

## 关键参数说明

### 锁定参数
```python
error_magnitude < 15    # 准星接近距离（像素）
stable_lock_count > 50  # 稳定帧数要求（约1.7秒）
```

### 归位参数
```python
no_target_count > 30    # 无目标帧数（约1秒）
reset_count > 200       # 重置等待帧数（约6.7秒）
```

### 舵机参数
```python
servo_x_center = 2048   # X轴中心位置
servo_y_center = 2048   # Y轴中心位置
position_scale = 10     # 位置计算放大系数
```

## 故障排除

### 第二问不锁定
**检查**：
- 是否检测到红色矩形？
- 准星是否真正接近矩形中心？
- 锁定计数是否在增加？

**解决**：
- 调整光照确保矩形检测
- 精确手动调整准星位置
- 观察"Lock: X/50"计数变化

### 第三问不归位
**检查**：
- 是否真正检测不到目标？
- "NoTarget: X/30"是否在增加？
- 是否显示了保存位置？

**解决**：
- 完全遮挡目标或移开设备
- 确认第二问已正确完成
- 检查"POS: X,Y"显示

### 归位位置不准确
**检查**：
- 第二问锁定时是否准确？
- 显示的"POS: X,Y"是否合理？
- 舵机是否正常响应？

**解决**：
- 重新精确执行第二问
- 使用重置工具重新开始
- 检查舵机机械连接

### 无法重复测试
**检查**：
- 是否等待了足够的重置时间？
- "Reset: X/200"计数是否在增加？

**解决**：
- 等待自动重置完成
- 使用手动重置工具
- 重新启动系统

## 优化建议

### 提高精度
```python
error_magnitude < 10    # 减小接近距离
stable_lock_count > 80  # 增加稳定要求
```

### 加快响应
```python
no_target_count > 20    # 减少无目标等待
reset_count > 100       # 减少重置等待
```

### 调整灵敏度
```python
position_scale = 8      # 减小放大系数
threshold = 20000       # 调整矩形检测阈值
```

## 比赛使用建议

### 第二问操作
1. **精确调整**：手动调整时要非常精确，准星必须完全对准靶心
2. **稳定保持**：看到锁定计数开始后，保持云台完全静止
3. **确认保存**：必须看到"POS: X,Y"显示才算完成

### 第三问操作
1. **完全移动**：评委改变位置时，确保目标完全离开视野
2. **耐心等待**：给系统足够时间检测无目标状态
3. **观察归位**：确认舵机移动到正确位置

**现在的版本支持无限次重复测试，第三问归位逻辑也已修复！**
