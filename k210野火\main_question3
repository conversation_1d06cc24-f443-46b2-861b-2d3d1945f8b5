# 第三问专用版本：快速定位系统
# 基于位置记录的快速回位功能

from machine import UART
import time
import sensor, image, lcd
from fpioa_manager import fm
import gc
from math import sqrt

# 硬件初始化
def hardware_init():
    """K210硬件初始化"""
    sensor.reset()
    sensor.set_pixformat(sensor.GRAYSCALE)
    sensor.set_framesize(sensor.QQVGA)
    sensor.set_brightness(-2)
    sensor.set_saturation(-2)
    sensor.skip_frames(time=2000)
    
    lcd.init()
    lcd.clear(lcd.WHITE)
    print("第三问系统初始化完成")

# 舵机控制函数
def func_servo(id0, posit0, interval0):
    """舵机控制函数"""
    ZT1 = 0xFF
    ZT2 = 0xFF
    DATA1 = 0X2A
    DATA2 = (posit0 >> 8) & 0xff
    DATA3 = posit0 & 0xff
    DATA4 = (interval0 >> 8) & 0xff
    DATA5 = interval0 & 0xff
    data_length = 0x07
    WriteDATA = 0X03
    GetChecksum = (~(id0 + data_length + WriteDATA + DATA1 + DATA2 + DATA3 + DATA4 + DATA5)) & 0xff
    text = bytes([ZT1, ZT2, id0, data_length, WriteDATA, DATA1, DATA2, DATA3, DATA4, DATA5, GetChecksum])
    uart.write(text)

def main():
    """第三问主程序 - 快速定位系统"""
    global uart
    
    hardware_init()
    
    # 基础参数
    clock = time.clock()
    target_x = 70
    target_y = 70
    
    # PID参数
    kp_x = 0
    ki_x = 1
    kp_y = 0
    ki_y = 1
    integral_x = 0
    integral_y = 0
    
    # 舵机参数
    servo_x_center = 2048
    servo_y_center = 2048
    servo_x_pos = servo_x_center
    servo_y_pos = servo_y_center
    interval = 100
    ID1 = 0x01
    ID2 = 0x02
    
    # 第三问核心功能：位置记录系统
    target_locked_x = None      # 锁定位置X
    target_locked_y = None      # 锁定位置Y
    no_target_count = 0         # 无目标计数
    fast_return_triggered = False  # 快速回位标志
    lock_stable_count = 0       # 锁定稳定计数
    
    # 串口初始化
    fm.register(0, fm.fpioa.UART1_RX, force=True)
    fm.register(1, fm.fpioa.UART1_TX, force=True)
    uart = UART(UART.UART1, 115200, read_buf_len=4096)
    
    # 舵机回中位
    func_servo(ID1, servo_x_center, 1000)
    func_servo(ID2, servo_y_center, 1000)
    time.sleep(1)
    
    print("第三问快速定位系统启动...")
    print("请先手动瞄准靶心，系统将自动记录位置")
    
    # 主循环
    while True:
        gc.collect()
        clock.tick()
        
        img = sensor.snapshot()
        img.laplacian(1, sharpen=True)
        
        # 矩形检测 - 降低阈值提高检测率
        rectangles = img.find_rects(threshold=25000)
        
        if rectangles:
            # 发现目标
            no_target_count = 0
            fast_return_triggered = False
            
            # 选择最大矩形
            largest_rect = max(rectangles, key=lambda r: r.w() * r.h())
            
            # 绘制所有矩形
            for r in rectangles:
                if r == largest_rect:
                    img.draw_rectangle(r.rect(), color=(255, 0, 0), thickness=3)
                else:
                    img.draw_rectangle(r.rect(), color=(128, 128, 128))
                for p in r.corners():
                    img.draw_circle(p[0], p[1], 3, color=(0, 255, 0))
            
            # 计算中心
            corners = largest_rect.corners()
            center_x = sum(p[0] for p in corners) / 4
            center_y = sum(p[1] for p in corners) / 4
            img.draw_circle(int(center_x), int(center_y), 8, color=(0, 0, 255))
            
            # PID控制
            error_x = center_x - target_x
            error_y = center_y - target_y
            error_magnitude = sqrt(error_x*error_x + error_y*error_y)
            
            # 积分项处理
            if abs(error_x) > 50 or abs(error_y) > 50:
                integral_x = 0
                integral_y = 0
                lock_stable_count = 0
            else:
                integral_x = error_x * ki_x + integral_x
                integral_y = error_y * ki_y + integral_y
            
            integral_x = max(-200, min(200, integral_x))
            integral_y = max(-200, min(200, integral_y))
            
            # PID输出
            output_x = error_x * kp_x + integral_x
            output_y = error_y * kp_y + integral_y
            
            # 智能死区控制
            if error_magnitude < 8:
                output_x *= 0.1
                output_y *= 0.1
                integral_x *= 0.7
                integral_y *= 0.7
                lock_stable_count += 1
            elif error_magnitude < 15:
                output_x *= 0.4
                output_y *= 0.4
                integral_x *= 0.9
                integral_y *= 0.9
                lock_stable_count = 0
            else:
                lock_stable_count = 0
            
            # 输出限制
            max_output = 300
            output_x = max(-max_output, min(max_output, output_x))
            output_y = max(-max_output, min(max_output, output_y))
            
            # 舵机位置计算
            servo_x_pos = servo_x_center - int(output_x)
            servo_y_pos = servo_y_center + int(output_y)
            servo_x_pos = max(1000, min(3000, servo_x_pos))
            servo_y_pos = max(1000, min(3000, servo_y_pos))
            
            # 舵机控制
            func_servo(ID1, servo_x_pos, interval)
            func_servo(ID2, servo_y_pos, interval)
            
            # 第三问核心：位置记录
            if error_magnitude < 8 and lock_stable_count > 5:
                # 稳定锁定5帧后记录位置
                target_locked_x = servo_x_pos
                target_locked_y = servo_y_pos
                img.draw_string(0, 60, "LOCKED & SAVED", color=(0, 255, 0), scale=2)
                img.draw_string(0, 75, "X:%d Y:%d" % (target_locked_x, target_locked_y), color=(0, 255, 0), scale=1)
            elif error_magnitude < 8:
                img.draw_string(0, 60, "LOCKING...", color=(255, 255, 0), scale=2)
                img.draw_string(0, 75, "Stable: %d/5" % lock_stable_count, color=(255, 255, 0), scale=1)
            else:
                img.draw_string(0, 60, "TRACKING", color=(0, 255, 255), scale=2)
            
            # 显示调试信息
            img.draw_string(0, 90, "Error: %.1f" % error_magnitude, color=(255, 255, 255), scale=1)
            
        else:
            # 无目标检测
            no_target_count += 1
            img.draw_string(0, 60, "NO TARGET", color=(255, 0, 0), scale=2)
            img.draw_string(0, 75, "Lost: %d frames" % no_target_count, color=(255, 255, 0), scale=1)
            
            # 第三问核心：快速回位
            if no_target_count > 3 and target_locked_x is not None and not fast_return_triggered:
                print("执行快速回位: X=%d, Y=%d" % (target_locked_x, target_locked_y))
                
                # 快速移动到记录位置
                func_servo(ID1, target_locked_x, 150)  # 150ms快速移动
                func_servo(ID2, target_locked_y, 150)
                
                servo_x_pos = target_locked_x
                servo_y_pos = target_locked_y
                fast_return_triggered = True
                
                img.draw_string(0, 90, "FAST RETURN!", color=(255, 0, 255), scale=2)
                img.draw_string(0, 105, "To: %d,%d" % (target_locked_x, target_locked_y), color=(255, 0, 255), scale=1)
                
            elif target_locked_x is None:
                img.draw_string(0, 90, "NO SAVED POSITION", color=(255, 255, 0), scale=1)
                img.draw_string(0, 105, "Please lock target first", color=(255, 255, 0), scale=1)
        
        # 绘制中心十字线
        img.draw_cross(target_x, target_y, color=(255, 255, 0), scale=4)
        
        # 显示系统状态
        fps = clock.fps()
        img.draw_string(0, 0, "Q3 System FPS:%.1f" % fps, color=(255, 0, 0), scale=1)
        
        if target_locked_x is not None:
            img.draw_string(0, 15, "Saved: X%d Y%d" % (target_locked_x, target_locked_y), color=(0, 255, 0), scale=1)
        else:
            img.draw_string(0, 15, "No position saved", color=(255, 255, 0), scale=1)
        
        lcd.display(img)

if __name__ == "__main__":
    main()
