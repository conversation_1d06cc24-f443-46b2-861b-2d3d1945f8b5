# 最终正确版本操作说明

## 系统功能理解

### 第二问：手动锁定 + 舵机保持
1. **K210上电**：显示准星，舵机不动
2. **手动调整**：手动调整云台，让准星对准靶心
3. **发送'S'信号**：舵机上电，记录位置并移动到锁定位置
4. **舵机保持**：舵机保持在锁定位置不动

### 第三问：舵机断电 + 重新上电归位
1. **舵机断电**：舵机失去动力
2. **评委改变位置**：整体设备位置和方向被改变
3. **舵机重新上电**：发送'S'信号，舵机重新上电
4. **自动归位**：舵机自动移动到第二问记录的位置

## 详细操作流程

### 第二问完整流程

#### 步骤1：系统启动
```
上传main_final代码 → K210上电 → 显示"Q2: MANUAL"
```

#### 步骤2：手动调整
- K210显示黄色准星十字
- **手动调整云台**，让准星精确对准靶心中央
- 此时舵机完全不动，纯手动调整

#### 步骤3：锁定位置
- 准星对准靶心后，发送'S'信号
- 系统计算当前准星与靶心的偏移
- 自动计算并记录需要的舵机位置
- 舵机移动到计算位置并保持

#### 步骤4：第二问完成
- 屏幕显示"Q2: LOCKED"
- 位置已保存到文件
- 舵机保持在锁定位置

### 第三问完整流程

#### 步骤1：舵机断电
```
舵机断电 → 失去动力 → 可能位置改变
```

#### 步骤2：评委改变位置
- 评委任意移动整个设备
- 改变云台位置和方向
- K210持续显示画面（不断电）

#### 步骤3：舵机重新上电
- 再次发送'S'信号
- 系统检测到有保存位置
- 自动进入第三问模式

#### 步骤4：自动归位
- 舵机自动移动到第二问记录的位置
- 准星重新回到靶心位置
- 显示"Q3: RETURNED"

## 屏幕状态说明

| 显示内容 | 含义 | 操作 |
|---------|------|------|
| `Q2: MANUAL` | 第二问手动调整模式 | 手动调整云台对准靶心 |
| `Q2: READY` | 已有保存位置，等待锁定 | 发送'S'进行锁定 |
| `Q2: LOCKED` | 第二问完成，位置已锁定 | 第二问完成，准备第三问 |
| `Q3: RETURNED` | 第三问归位完成 | 准星已回到靶心位置 |

## 关键技术点

### 位置计算逻辑
```python
# 计算准星与靶心的偏移
error_x = center_x - target_x
error_y = center_y - target_y

# 计算需要的舵机位置
target_locked_x = servo_x_center - int(error_x * 10)
target_locked_y = servo_y_center + int(error_y * 10)
```

### 舵机上电检测
```python
if text_str == 'S':  # 检测到舵机上电信号
    servo_power_count += 1  # 记录上电次数
    
    if 第一次上电 and 无保存位置:
        执行第二问：记录位置并锁定
    elif 有保存位置:
        执行第三问：自动归位
```

## 测试验证

### 第二问测试
1. 上传代码，K210上电
2. 手动调整云台，准星对准靶心
3. 发送'S'信号
4. 观察舵机移动并锁定
5. 确认显示"Q2: LOCKED"

### 第三问测试
1. 舵机断电（或模拟断电）
2. 移动整个设备到不同位置
3. 发送'S'信号（舵机重新上电）
4. 观察舵机自动归位
5. 确认准星回到靶心，显示"Q3: RETURNED"

## 信号发送方法

### 方法1：串口工具
使用串口调试工具发送字符'S'

### 方法2：代码模拟
在代码中添加：
```python
# 模拟舵机上电信号
uart.write(b'S')
```

### 方法3：按键触发
可以添加按键检测代码触发'S'信号

## 故障排除

### 问题1：第二问不锁定
- **检查**：是否检测到矩形靶子
- **解决**：确保靶子在视野内且清晰可见

### 问题2：位置计算不准确
- **检查**：手动调整时准星是否精确对准靶心
- **解决**：重新精确调整后再发送'S'

### 问题3：第三问不归位
- **检查**：是否有保存的位置文件
- **解决**：确认第二问已正确完成

### 问题4：归位位置偏差
- **检查**：第二问锁定时是否真正准确
- **解决**：删除保存文件，重新执行第二问

## 重置方法

删除保存位置，重新开始：
```python
import os
try:
    os.remove('/flash/saved_position.txt')
    print("位置文件已删除")
except:
    print("文件不存在")
```

## 系统优势

1. **真正的手动+自动结合**：第二问完全手动调整，第三问完全自动归位
2. **精确位置记录**：基于视觉计算的精确舵机位置
3. **断电重连支持**：支持舵机断电重新上电的真实场景
4. **简单可靠**：逻辑清晰，操作简单
5. **完全符合比赛要求**：准星必须回到第二问记录的位置

**这个版本完全理解并实现了您的需求！**
