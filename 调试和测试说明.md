# 调试和测试说明

## 修复内容

### 问题1：第二问锁定确认
**原问题**：不确定是否锁定了坐标
**修复方案**：
- 增加`position_saved`标志确认位置保存状态
- 屏幕显示"POS SAVED"确认位置已保存
- 启动时自动检测保存文件，如有则显示"Q2: LOCKED"

### 问题2：第三问无法归位
**原问题**：第三问不能自动归位
**修复方案**：
- 增加`no_target_count`计数器
- 连续30帧检测不到目标时自动归位
- 屏幕显示"NoTarget: X"显示无目标计数

## 测试步骤

### 第二问测试

#### 步骤1：确认初始状态
```
上传main_stable_simple → K210上电 → 观察屏幕显示
预期结果：
- 如果无保存文件：显示"Q2: AIM"
- 如果有保存文件：显示"Q2: LOCKED" + "POS SAVED"
```

#### 步骤2：手动调整锁定
```
手动调整云台 → 准星对准红色矩形 → 观察锁定计数
预期结果：
- 准星接近时显示"Lock: X/50"
- 计数到50时自动锁定
- 显示"Q2: LOCKED" + "POS SAVED"
- 舵机移动到锁定位置
```

#### 步骤3：确认位置保存
```
检查文件系统 → 确认/flash/saved_position.txt存在
或者重启K210 → 观察是否显示"Q2: LOCKED"
```

### 第三问测试

#### 步骤1：模拟目标丢失
```
方法1：遮挡摄像头
方法2：移动设备让目标离开视野
方法3：改变光照让矩形检测失效
```

#### 步骤2：观察归位过程
```
目标丢失 → 观察"NoTarget: X"计数
预期结果：
- 计数从1开始增加
- 到达30时自动归位
- 显示"Q3: RETURNED"
- 舵机移动到保存位置
```

## 屏幕状态说明

### 正常流程状态
| 显示内容 | 含义 | 下一步 |
|---------|------|--------|
| `Q2: AIM` | 第二问瞄准模式 | 手动调整准星对准目标 |
| `Lock: X/50` | 正在锁定计数 | 保持准星稳定 |
| `Q2: LOCKED` + `POS SAVED` | 第二问完成，位置已保存 | 可以测试第三问 |
| `NoTarget: X` | 检测不到目标计数 | 等待自动归位 |
| `Q3: RETURNED` + `BACK TO TARGET` | 第三问归位完成 | 测试成功 |

### 异常状态处理
| 显示内容 | 可能原因 | 解决方法 |
|---------|----------|----------|
| `Q2: AIM` 不变 | 检测不到矩形 | 调整光照，确保目标清晰 |
| `Lock: X/50` 重置 | 准星偏离目标 | 重新精确调整准星位置 |
| `Q2: LOCKED` 无 `POS SAVED` | 文件保存失败 | 检查flash存储空间 |
| `NoTarget: X` 不增加 | 仍能检测到目标 | 完全遮挡或移开目标 |

## 调试方法

### 方法1：检查保存文件
```python
# 在K210上执行
import os
try:
    with open('/flash/saved_position.txt', 'r') as f:
        print("保存位置:", f.read())
except:
    print("无保存文件")
```

### 方法2：手动删除保存文件
```python
# 重新测试第二问
import os
try:
    os.remove('/flash/saved_position.txt')
    print("文件已删除")
except:
    print("文件不存在")
```

### 方法3：手动触发归位
```python
# 在代码中添加测试
func_servo(ID1, target_locked_x, 300)
func_servo(ID2, target_locked_y, 300)
```

## 关键参数调整

### 锁定敏感度
```python
error_magnitude < 15  # 准星接近距离（像素）
stable_lock_count > 50  # 稳定帧数要求
```

### 归位触发
```python
no_target_count > 30  # 无目标帧数阈值
```

### 舵机位置
```python
target_locked_x = servo_x_center - int(error_x * 10)  # X轴计算
target_locked_y = servo_y_center + int(error_y * 10)  # Y轴计算
```

## 常见问题解决

### 问题1：第二问不锁定
**检查项**：
- 是否检测到红色矩形框？
- 准星是否真正接近矩形中心？
- 锁定计数是否在增加？

**解决方法**：
- 调整光照确保矩形检测
- 精确手动调整准星位置
- 降低error_magnitude阈值

### 问题2：锁定位置不准确
**检查项**：
- 手动调整时准星是否精确对准？
- 舵机是否正常响应？
- 计算参数是否合适？

**解决方法**：
- 重新精确调整
- 检查舵机连接
- 调整放大系数（当前为10）

### 问题3：第三问不归位
**检查项**：
- 是否真正检测不到目标？
- no_target_count是否在增加？
- 是否有保存位置？

**解决方法**：
- 完全遮挡目标或移开设备
- 观察屏幕计数显示
- 确认第二问已正确完成

### 问题4：归位位置偏差
**检查项**：
- 第二问锁定时是否准确？
- 舵机是否正常工作？
- 保存的位置数据是否正确？

**解决方法**：
- 重新执行第二问
- 检查舵机机械结构
- 删除保存文件重新开始

## 性能优化建议

### 1. 提高锁定精度
- 减小error_magnitude阈值（如改为10）
- 增加stable_lock_count要求（如改为80）

### 2. 加快归位响应
- 减少no_target_count阈值（如改为20）
- 提高归位速度（减少interval时间）

### 3. 增强稳定性
- 增加异常处理
- 添加舵机通信检查
- 优化图像处理参数

**现在的版本应该能够正确显示第二问锁定状态和第三问归位功能！**
