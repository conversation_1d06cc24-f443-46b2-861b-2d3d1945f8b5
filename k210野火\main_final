# 最终正确版本：第二问手动锁定+第三问舵机断电重新上电归位
# 第二问：K210上电显示准星 → 手动调整锁定 → 记录位置 → 舵机上电保持
# 第三问：舵机断电 → 评委改变位置 → 舵机重新上电 → 自动归位

from machine import UART
import time
import sensor, image, lcd
from fpioa_manager import fm
import gc
from math import sqrt

def func_servo(id0, posit0, interval0):
    """舵机控制函数"""
    try:
        ZT1 = 0xFF
        ZT2 = 0xFF
        DATA1 = 0X2A
        DATA2 = (posit0 >> 8) & 0xff
        DATA3 = posit0 & 0xff
        DATA4 = (interval0 >> 8) & 0xff
        DATA5 = interval0 & 0xff
        data_length = 0x07
        WriteDATA = 0X03
        GetChecksum = (~(id0 + data_length + WriteDATA + DATA1 + DATA2 + DATA3 + DATA4 + DATA5)) & 0xff
        text = bytes([ZT1, ZT2, id0, data_length, WriteDA<PERSON>, DATA1, DATA2, <PERSON>ATA3, <PERSON><PERSON>A4, DATA5, <PERSON><PERSON><PERSON><PERSON><PERSON>])
        uart.write(text)
    except:
        pass

def main():
    """主程序"""
    global uart
    
    # 硬件初始化
    try:
        sensor.reset()
        sensor.set_pixformat(sensor.GRAYSCALE)
        sensor.set_framesize(sensor.QQVGA)
        sensor.set_brightness(-2)
        sensor.set_saturation(-2)
        sensor.skip_frames(time=2000)
        
        lcd.init()
        lcd.clear(lcd.WHITE)
        print("K210硬件初始化完成")
    except Exception as e:
        print("硬件初始化失败:", e)
        return
    
    # 参数初始化
    clock = time.clock()
    target_x = 70
    target_y = 70
    
    # 舵机参数
    servo_x_center = 2048
    servo_y_center = 2048
    interval = 100
    ID1 = 0x01
    ID2 = 0x02
    
    # 功能变量
    target_locked_x = None
    target_locked_y = None
    servo_power_count = 0  # 舵机上电次数计数

    # 模式状态
    mode = "Q2_MANUAL"  # Q2_MANUAL=第二问手动调整, Q2_SERVO=舵机保持, Q3=自动归位

    # 舵机状态检测
    servo_online = False  # 舵机是否在线
    servo_check_count = 0  # 舵机检测计数
    last_servo_online = False  # 上次舵机状态
    stable_lock_count = 0  # 稳定锁定计数
    
    # 串口初始化
    try:
        fm.register(0, fm.fpioa.UART1_RX, force=True)
        fm.register(1, fm.fpioa.UART1_TX, force=True)
        uart = UART(UART.UART1, 115200, read_buf_len=4096)
        print("串口初始化完成")
    except Exception as e:
        print("串口初始化失败:", e)
        return
    
    # 尝试读取保存的位置
    try:
        with open('/flash/saved_position.txt', 'r') as f:
            data = f.read().strip().split(',')
            target_locked_x = int(data[0])
            target_locked_y = int(data[1])
            print("发现保存位置: X=%d, Y=%d" % (target_locked_x, target_locked_y))
            print("等待舵机上电信号进入第三问模式")
    except:
        print("未发现保存位置，进入第二问模式")
    
    print("系统启动完成")
    
    # 主循环
    while True:
        try:
            # 基础操作
            gc.collect()
            clock.tick()
            
            # 图像采集
            img = sensor.snapshot()
            if img is None:
                continue
            
            # 检测舵机是否在线（通过尝试发送命令）
            servo_check_count += 1
            if servo_check_count % 10 == 0:  # 每10帧检测一次
                try:
                    # 发送位置查询命令测试舵机是否在线
                    func_servo(ID1, servo_x_center, 0)  # 发送测试命令
                    time.sleep(0.01)

                    # 检查是否有响应（简化检测）
                    response = uart.read()
                    if response and len(response) > 0:
                        servo_online = True
                    else:
                        servo_online = False
                except:
                    servo_online = False

            # 检测舵机上电状态变化
            if servo_online and not last_servo_online:
                # 舵机从离线变为在线（上电）
                servo_power_count += 1
                print("检测到舵机上电，次数:", servo_power_count)

                if mode == "Q2_MANUAL" and target_locked_x is None:
                    # 第二问：第一次舵机上电，等待手动锁定
                    print("第二问：舵机已上电，请手动调整准星对准靶心")
                    mode = "Q2_READY"

                elif target_locked_x is not None:
                    # 第三问：有保存位置，舵机重新上电后自动归位
                    print("第三问：舵机重新上电，自动归位到记录位置")
                    func_servo(ID1, target_locked_x, 300)
                    func_servo(ID2, target_locked_y, 300)
                    mode = "Q3"
                    print("第三问归位完成")

            last_servo_online = servo_online
            
            # 图像处理
            img.laplacian(1, sharpen=True)
            
            # 矩形检测和显示
            rectangles = img.find_rects(threshold=25000)
            if rectangles:
                r = rectangles[0]
                img.draw_rectangle(r.rect(), color=(255, 0, 0))

                # 计算中心并显示
                corners = r.corners()
                center_x = sum(p[0] for p in corners) / 4
                center_y = sum(p[1] for p in corners) / 4
                img.draw_circle(int(center_x), int(center_y), 5, color=(0, 255, 0))

                # 第二问：检测稳定锁定
                if mode == "Q2_READY" and servo_online:
                    error_x = center_x - target_x
                    error_y = center_y - target_y
                    error_magnitude = sqrt(error_x*error_x + error_y*error_y)

                    if error_magnitude < 15:  # 准星接近靶心
                        stable_lock_count += 1
                        if stable_lock_count > 30:  # 稳定30帧后自动锁定
                            # 计算需要的舵机位置
                            target_locked_x = servo_x_center - int(error_x * 10)
                            target_locked_y = servo_y_center + int(error_y * 10)

                            # 限制范围
                            target_locked_x = max(1000, min(3000, target_locked_x))
                            target_locked_y = max(1000, min(3000, target_locked_y))

                            # 保存位置
                            try:
                                with open('/flash/saved_position.txt', 'w') as f:
                                    f.write("%d,%d" % (target_locked_x, target_locked_y))
                                print("第二问位置已保存: X=%d, Y=%d" % (target_locked_x, target_locked_y))

                                # 舵机移动到锁定位置并保持
                                func_servo(ID1, target_locked_x, 500)
                                func_servo(ID2, target_locked_y, 500)
                                mode = "Q2_SERVO"
                                print("第二问完成：舵机已锁定位置")
                            except:
                                print("位置保存失败")
                    else:
                        stable_lock_count = 0
            
            # 显示准星
            img.draw_cross(target_x, target_y, color=(255, 255, 0), scale=4)
            
            # 状态显示
            if mode == "Q2_MANUAL":
                img.draw_string(0, 0, "Q2: MANUAL", color=(255, 255, 0), scale=2)
                img.draw_string(0, 20, "Turn on servo power", color=(255, 255, 255), scale=1)
            elif mode == "Q2_READY":
                img.draw_string(0, 0, "Q2: READY", color=(0, 255, 255), scale=2)
                img.draw_string(0, 20, "Aim target manually", color=(255, 255, 255), scale=1)
                if stable_lock_count > 0:
                    img.draw_string(0, 40, "Locking: %d/30" % stable_lock_count, color=(255, 255, 0), scale=1)
            elif mode == "Q2_SERVO":
                img.draw_string(0, 0, "Q2: LOCKED", color=(0, 255, 0), scale=2)
                img.draw_string(0, 20, "Position saved", color=(0, 255, 0), scale=1)
                img.draw_string(0, 40, "Ready for Q3", color=(0, 255, 0), scale=1)
            elif mode == "Q3":
                img.draw_string(0, 0, "Q3: RETURNED", color=(0, 255, 0), scale=2)
                img.draw_string(0, 20, "Back to target!", color=(0, 255, 0), scale=1)

            # 显示舵机状态
            servo_status = "ONLINE" if servo_online else "OFFLINE"
            img.draw_string(0, 100, "Servo: %s (%d)" % (servo_status, servo_power_count), color=(255, 0, 0), scale=1)
            
            lcd.display(img)
            
        except Exception as e:
            print("循环错误:", e)
            time.sleep(0.1)
            continue

if __name__ == "__main__":
    main()
